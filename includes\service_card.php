<?php
/**
 * Service Card Component
 * Reusable service card for displaying services
 */
?>
<div class="service-card bg-secondary-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] group border border-secondary-700 hover:border-salon-gold/30">
    <!-- Service Image -->
    <div class="relative bg-secondary-700 overflow-hidden">
        <?php if ($service['image']): ?>
            <?php
            // Check if image is a URL or uploaded file
            $imageSrc = filter_var($service['image'], FILTER_VALIDATE_URL)
                ? $service['image']
                : getBasePath() . '/uploads/' . $service['image'];
            ?>
            <img src="<?= htmlspecialchars($imageSrc) ?>"
                 alt="<?= htmlspecialchars($service['name']) ?>"
                 class="w-full h-32 object-cover group-hover:scale-110 transition-transform duration-300"
                 loading="lazy">
        <?php else: ?>
            <div class="w-full h-32 bg-gradient-to-br from-salon-gold/20 to-secondary-700 flex items-center justify-center">
                <svg class="w-10 h-10 text-salon-gold/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4m-6-4a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2z"></path>
                </svg>
            </div>
        <?php endif; ?>

        <!-- Price Badge -->
        <div class="absolute top-2 right-2 bg-salon-gold text-black px-2 py-1 rounded-full text-sm font-bold">
            <?= formatCurrency($service['price']) ?>
        </div>

        <!-- Subcategory Badge (if applicable) -->
        <?php if (isset($service['subcategory_name']) && $service['subcategory_name']): ?>
            <div class="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                <?= htmlspecialchars($service['subcategory_name']) ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Service Content -->
    <div class="p-4">
        <div class="mb-3">
            <h3 class="text-lg font-semibold text-white group-hover:text-salon-gold transition-colors mb-1 line-clamp-1">
                <?= htmlspecialchars($service['name']) ?>
            </h3>
            <?php if ($service['description']): ?>
                <p class="text-gray-400 text-sm line-clamp-2 mb-3">
                    <?= htmlspecialchars($service['description']) ?>
                </p>
            <?php endif; ?>
        </div>

        <!-- Service Info -->
        <div class="flex items-center justify-between mb-4 text-sm">
            <div class="flex items-center text-gray-400">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span><?= $service['duration'] ?> min</span>
            </div>
            <div class="flex items-center text-salon-gold">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
                <span>Premium</span>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-2">
            <button onclick="checkServiceVariations('<?= $service['id'] ?>', '<?= htmlspecialchars($service['name']) ?>', <?= $service['price'] ?>, <?= $service['duration'] ?>)"
                    class="flex-1 bg-salon-gold text-black py-2 px-3 rounded-lg text-sm font-semibold hover:bg-gold-light transition-colors">
                Book Now
            </button>
            <button onclick="viewServiceDetails('<?= $service['id'] ?>')"
                    class="px-3 py-2 border border-salon-gold text-salon-gold rounded-lg hover:bg-salon-gold hover:text-black transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </button>

            <!-- Wishlist Heart Icon -->
            <button onclick="toggleWishlist('service', '<?= $service['id'] ?>', this)"
                    class="wishlist-btn px-3 py-2 border-2 border-gray-500 text-gray-300 rounded-lg hover:border-red-400 hover:text-red-400 transition-all duration-300"
                    data-item-type="service"
                    data-item-id="<?= $service['id'] ?>"
                    title="Add to wishlist">
                <i class="far fa-heart text-base"></i>
            </button>
        </div>
    </div>
</div>
